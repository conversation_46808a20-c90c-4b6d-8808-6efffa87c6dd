{"swagger": "2.0", "info": {"description": "RESTful Swagger API 生成器", "version": "1.0.0", "title": "Swagger 脚本生成器", "termsOfService": "admin", "contact": {"email": "admin"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}}, "host": "petstore.swagger.io", "basePath": "/v2", "tags": [{"name": "BrandController", "description": "BrandController"}, {"name": "BrandController", "description": "BrandController"}, {"name": "BrandController", "description": "BrandController"}, {"name": "BrandController", "description": "BrandController"}, {"name": "CategoryController", "description": "CategoryController"}, {"name": "CategoryController", "description": "CategoryController"}, {"name": "CategoryController", "description": "CategoryController"}, {"name": "CategoryController", "description": "CategoryController"}, {"name": "CategoryBrandController", "description": "CategoryBrandController"}, {"name": "CategoryBrandController", "description": "CategoryBrandController"}, {"name": "CategoryBrandController", "description": "CategoryBrandController"}, {"name": "CategoryBrandController", "description": "CategoryBrandController"}, {"name": "SkuController", "description": "SkuController"}, {"name": "SkuController", "description": "SkuController"}, {"name": "SkuController", "description": "SkuController"}, {"name": "SkuController", "description": "SkuController"}, {"name": "SkuAttributeController", "description": "SkuAttributeController"}, {"name": "SkuAttributeController", "description": "SkuAttributeController"}, {"name": "SkuAttributeController", "description": "SkuAttributeController"}, {"name": "SkuAttributeController", "description": "SkuAttributeController"}, {"name": "SpuController", "description": "SpuController"}, {"name": "SpuController", "description": "SpuController"}, {"name": "SpuController", "description": "SpuController"}, {"name": "SpuController", "description": "SpuController"}], "schemes": ["http"], "paths": {"/brand": {"get": {"tags": ["BrandController"], "summary": "查询所有Brand", "description": "查询所有Brand方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有Brand", "schema": {"$ref": "#/definitions/RespResult«List«Brand»»"}}}}, "post": {"tags": ["BrandController"], "summary": "添加Brand", "description": "添加Brand方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加Brand方法详情", "required": true, "schema": {"$ref": "#/definitions/Brand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加Brand", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/brand/{id}": {"delete": {"tags": ["BrandController"], "summary": "根据ID删除Brand", "description": "根据ID删除Brand方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除Brand方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除Brand", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["BrandController"], "summary": "根据ID修改Brand", "description": "根据ID修改Brand方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Brand方法详情", "required": true}, {"in": "body", "name": "Brand", "description": "传入Brand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Brand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Brand", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["BrandController"], "summary": "根据ID查询Brand", "description": "根据ID查询Brand方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Brand方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Brand", "schema": {"$ref": "#/definitions/RespResult«Brand»"}}}}}, "/brand/search": {"post": {"tags": ["BrandController"], "summary": "不带分页条件搜索Brand", "description": "不带分页条件搜索Brand方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "Brand", "description": "传入Brand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Brand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Brand", "schema": {"$ref": "#/definitions/RespResult«List«Brand»»"}}}}}, "/brand/search/{page}/{size}": {"get": {"tags": ["BrandController"], "summary": "分页列表查询Brand", "description": "分页列表查询Brand方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Brand", "schema": {"$ref": "#/definitions/RespResult«List«Brand»»"}}}}, "post": {"tags": ["BrandController"], "summary": "分页条件搜索Brand", "description": "分页条件搜索Brand方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "Brand", "description": "传入Brand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Brand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索Brand", "schema": {"$ref": "#/definitions/RespResult«List«Brand»»"}}}}}, "/category": {"get": {"tags": ["CategoryController"], "summary": "查询所有Category", "description": "查询所有Category方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有Category", "schema": {"$ref": "#/definitions/RespResult«List«Category»»"}}}}, "post": {"tags": ["CategoryController"], "summary": "添加Category", "description": "添加Category方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加Category方法详情", "required": true, "schema": {"$ref": "#/definitions/Category"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加Category", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/category/{id}": {"delete": {"tags": ["CategoryController"], "summary": "根据ID删除Category", "description": "根据ID删除Category方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除Category方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除Category", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["CategoryController"], "summary": "根据ID修改Category", "description": "根据ID修改Category方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Category方法详情", "required": true}, {"in": "body", "name": "Category", "description": "传入Category的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Category"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Category", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["CategoryController"], "summary": "根据ID查询Category", "description": "根据ID查询Category方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Category方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Category", "schema": {"$ref": "#/definitions/RespResult«Category»"}}}}}, "/category/search": {"post": {"tags": ["CategoryController"], "summary": "不带分页条件搜索Category", "description": "不带分页条件搜索Category方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "Category", "description": "传入Category的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Category"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Category", "schema": {"$ref": "#/definitions/RespResult«List«Category»»"}}}}}, "/category/search/{page}/{size}": {"get": {"tags": ["CategoryController"], "summary": "分页列表查询Category", "description": "分页列表查询Category方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Category", "schema": {"$ref": "#/definitions/RespResult«List«Category»»"}}}}, "post": {"tags": ["CategoryController"], "summary": "分页条件搜索Category", "description": "分页条件搜索Category方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "Category", "description": "传入Category的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Category"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索Category", "schema": {"$ref": "#/definitions/RespResult«List«Category»»"}}}}}, "/categoryBrand": {"get": {"tags": ["CategoryBrandController"], "summary": "查询所有CategoryBrand", "description": "查询所有CategoryBrand方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有CategoryBrand", "schema": {"$ref": "#/definitions/RespResult«List«CategoryBrand»»"}}}}, "post": {"tags": ["CategoryBrandController"], "summary": "添加CategoryBrand", "description": "添加CategoryBrand方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加CategoryBrand方法详情", "required": true, "schema": {"$ref": "#/definitions/CategoryBrand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加CategoryBrand", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/categoryBrand/{id}": {"delete": {"tags": ["CategoryBrandController"], "summary": "根据ID删除CategoryBrand", "description": "根据ID删除CategoryBrand方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除CategoryBrand方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除CategoryBrand", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["CategoryBrandController"], "summary": "根据ID修改CategoryBrand", "description": "根据ID修改CategoryBrand方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改CategoryBrand方法详情", "required": true}, {"in": "body", "name": "CategoryBrand", "description": "传入CategoryBrand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/CategoryBrand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改CategoryBrand", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["CategoryBrandController"], "summary": "根据ID查询CategoryBrand", "description": "根据ID查询CategoryBrand方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改CategoryBrand方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改CategoryBrand", "schema": {"$ref": "#/definitions/RespResult«CategoryBrand»"}}}}}, "/categoryBrand/search": {"post": {"tags": ["CategoryBrandController"], "summary": "不带分页条件搜索CategoryBrand", "description": "不带分页条件搜索CategoryBrand方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "CategoryBrand", "description": "传入CategoryBrand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/CategoryBrand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索CategoryBrand", "schema": {"$ref": "#/definitions/RespResult«List«CategoryBrand»»"}}}}}, "/categoryBrand/search/{page}/{size}": {"get": {"tags": ["CategoryBrandController"], "summary": "分页列表查询CategoryBrand", "description": "分页列表查询CategoryBrand方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索CategoryBrand", "schema": {"$ref": "#/definitions/RespResult«List«CategoryBrand»»"}}}}, "post": {"tags": ["CategoryBrandController"], "summary": "分页条件搜索CategoryBrand", "description": "分页条件搜索CategoryBrand方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "CategoryBrand", "description": "传入CategoryBrand的JSON对象", "required": false, "schema": {"$ref": "#/definitions/CategoryBrand"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索CategoryBrand", "schema": {"$ref": "#/definitions/RespResult«List«CategoryBrand»»"}}}}}, "/sku": {"get": {"tags": ["SkuController"], "summary": "查询所有Sku", "description": "查询所有Sku方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有Sku", "schema": {"$ref": "#/definitions/RespResult«List«Sku»»"}}}}, "post": {"tags": ["SkuController"], "summary": "添加Sku", "description": "添加Sku方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加Sku方法详情", "required": true, "schema": {"$ref": "#/definitions/Sku"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加Sku", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/sku/{id}": {"delete": {"tags": ["SkuController"], "summary": "根据ID删除Sku", "description": "根据ID删除Sku方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除Sku方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除Sku", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["SkuController"], "summary": "根据ID修改Sku", "description": "根据ID修改Sku方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Sku方法详情", "required": true}, {"in": "body", "name": "S<PERSON>", "description": "传入Sku的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Sku"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Sku", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["SkuController"], "summary": "根据ID查询Sku", "description": "根据ID查询Sku方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Sku方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Sku", "schema": {"$ref": "#/definitions/RespResult«Sku»"}}}}}, "/sku/search": {"post": {"tags": ["SkuController"], "summary": "不带分页条件搜索Sku", "description": "不带分页条件搜索Sku方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "S<PERSON>", "description": "传入Sku的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Sku"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Sku", "schema": {"$ref": "#/definitions/RespResult«List«Sku»»"}}}}}, "/sku/search/{page}/{size}": {"get": {"tags": ["SkuController"], "summary": "分页列表查询Sku", "description": "分页列表查询Sku方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Sku", "schema": {"$ref": "#/definitions/RespResult«List«Sku»»"}}}}, "post": {"tags": ["SkuController"], "summary": "分页条件搜索Sku", "description": "分页条件搜索Sku方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "S<PERSON>", "description": "传入Sku的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Sku"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索Sku", "schema": {"$ref": "#/definitions/RespResult«List«Sku»»"}}}}}, "/skuAttribute": {"get": {"tags": ["SkuAttributeController"], "summary": "查询所有SkuAttribute", "description": "查询所有SkuAttribute方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有SkuAttribute", "schema": {"$ref": "#/definitions/RespResult«List«SkuAttribute»»"}}}}, "post": {"tags": ["SkuAttributeController"], "summary": "添加SkuAttribute", "description": "添加SkuAttribute方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加SkuAttribute方法详情", "required": true, "schema": {"$ref": "#/definitions/SkuAttribute"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加SkuAttribute", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/skuAttribute/{id}": {"delete": {"tags": ["SkuAttributeController"], "summary": "根据ID删除SkuAttribute", "description": "根据ID删除SkuAttribute方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除SkuAttribute方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除SkuAttribute", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["SkuAttributeController"], "summary": "根据ID修改SkuAttribute", "description": "根据ID修改SkuAttribute方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改SkuAttribute方法详情", "required": true}, {"in": "body", "name": "SkuAttribute", "description": "传入SkuAttribute的JSON对象", "required": false, "schema": {"$ref": "#/definitions/SkuAttribute"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改SkuAttribute", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["SkuAttributeController"], "summary": "根据ID查询SkuAttribute", "description": "根据ID查询SkuAttribute方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改SkuAttribute方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改SkuAttribute", "schema": {"$ref": "#/definitions/RespResult«SkuAttribute»"}}}}}, "/skuAttribute/search": {"post": {"tags": ["SkuAttributeController"], "summary": "不带分页条件搜索SkuAttribute", "description": "不带分页条件搜索SkuAttribute方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "SkuAttribute", "description": "传入SkuAttribute的JSON对象", "required": false, "schema": {"$ref": "#/definitions/SkuAttribute"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索SkuAttribute", "schema": {"$ref": "#/definitions/RespResult«List«SkuAttribute»»"}}}}}, "/skuAttribute/search/{page}/{size}": {"get": {"tags": ["SkuAttributeController"], "summary": "分页列表查询SkuAttribute", "description": "分页列表查询SkuAttribute方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索SkuAttribute", "schema": {"$ref": "#/definitions/RespResult«List«SkuAttribute»»"}}}}, "post": {"tags": ["SkuAttributeController"], "summary": "分页条件搜索SkuAttribute", "description": "分页条件搜索SkuAttribute方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "SkuAttribute", "description": "传入SkuAttribute的JSON对象", "required": false, "schema": {"$ref": "#/definitions/SkuAttribute"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索SkuAttribute", "schema": {"$ref": "#/definitions/RespResult«List«SkuAttribute»»"}}}}}, "/spu": {"get": {"tags": ["SpuController"], "summary": "查询所有Spu", "description": "查询所有Spu方法详情", "operationId": "findAllUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "查询所有Spu", "schema": {"$ref": "#/definitions/RespResult«List«Spu»»"}}}}, "post": {"tags": ["SpuController"], "summary": "添加Spu", "description": "添加Spu方法详情", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "添加Spu方法详情", "required": true, "schema": {"$ref": "#/definitions/Spu"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "添加Spu", "schema": {"$ref": "#/definitions/RespResult"}}}}}, "/spu/{id}": {"delete": {"tags": ["SpuController"], "summary": "根据ID删除Spu", "description": "根据ID删除Spu方法详情", "operationId": "deleteUsingDELETE", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID删除Spu方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID删除Spu", "schema": {"$ref": "#/definitions/RespResult"}}}}, "put": {"tags": ["SpuController"], "summary": "根据ID修改Spu", "description": "根据ID修改Spu方法详情", "operationId": "updateUsingPUT", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Spu方法详情", "required": true}, {"in": "body", "name": "Spu", "description": "传入Spu的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Spu"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Spu", "schema": {"$ref": "#/definitions/RespResult"}}}}, "get": {"tags": ["SpuController"], "summary": "根据ID查询Spu", "description": "根据ID查询Spu方法详情", "operationId": "findByIdUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "id", "description": "根据ID修改Spu方法详情", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "根据ID修改Spu", "schema": {"$ref": "#/definitions/RespResult«Spu»"}}}}}, "/spu/search": {"post": {"tags": ["SpuController"], "summary": "不带分页条件搜索Spu", "description": "不带分页条件搜索Spu方法详情", "operationId": "findListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "Spu", "description": "传入Spu的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Spu"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Spu", "schema": {"$ref": "#/definitions/RespResult«List«Spu»»"}}}}}, "/spu/search/{page}/{size}": {"get": {"tags": ["SpuController"], "summary": "分页列表查询Spu", "description": "分页列表查询Spu方法详情", "operationId": "findPageUsingGET", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "不带分页搜索Spu", "schema": {"$ref": "#/definitions/RespResult«List«Spu»»"}}}}, "post": {"tags": ["SpuController"], "summary": "分页条件搜索Spu", "description": "分页条件搜索Spu方法详情", "operationId": "findPageUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "path", "name": "page", "description": "当前页", "required": true}, {"in": "path", "name": "size", "description": "每页显示条数", "required": true}, {"in": "body", "name": "Spu", "description": "传入Spu的JSON对象", "required": false, "schema": {"$ref": "#/definitions/Spu"}}], "responses": {"400": {"description": "Invalid status value(无效的状态值)"}, "403": {"description": "403 Forbidden(请求被拒绝)"}, "404": {"description": "not found(没有找到相关资源)"}, "405": {"description": "Invalid input(无效的输入)"}, "500": {"description": "服务器内部错误"}, "200": {"description": "分页条件搜索Spu", "schema": {"$ref": "#/definitions/RespResult«List«Spu»»"}}}}}}, "definitions": {"RespResult": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "object", "description": "逻辑数据"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«List«Brand»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/Brand"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«Brand»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/Brand"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "Brand": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "品牌id"}, "name": {"type": "string", "description": "品牌名称"}, "image": {"type": "string", "description": "品牌图片地址"}, "initial": {"type": "string", "description": "品牌的首字母"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}}, "description": "Brand"}, "RespResult«List«Category»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/Category"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«Category»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/Category"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "Category": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "分类ID"}, "name": {"type": "string", "description": "分类名称"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "parentId": {"type": "integer", "format": "int32", "description": "上级ID"}}, "description": "Category"}, "RespResult«List«CategoryBrand»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/CategoryBrand"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«CategoryBrand»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/CategoryBrand"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "CategoryBrand": {"type": "object", "properties": {"categoryId": {"type": "integer", "format": "int32", "description": "分类ID"}, "brandId": {"type": "integer", "format": "int32", "description": "品牌ID"}}, "description": "CategoryBrand"}, "RespResult«List«Sku»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/Sku"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«Sku»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/Sku"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "Sku": {"type": "object", "properties": {"id": {"type": "string", "description": "商品id"}, "name": {"type": "string", "description": "SKU名称"}, "price": {"type": "integer", "format": "int32", "description": "价格（分）"}, "num": {"type": "integer", "format": "int32", "description": "库存数量"}, "image": {"type": "string", "description": "商品图片"}, "images": {"type": "string", "description": "商品图片列表"}, "createTime": {"type": "date", "description": "创建时间"}, "updateTime": {"type": "date", "description": "更新时间"}, "spuId": {"type": "string", "description": "SPUID"}, "categoryId": {"type": "integer", "format": "int32", "description": "类目ID"}, "categoryName": {"type": "string", "description": "类目名称"}, "brandName": {"type": "string", "description": "品牌名称"}, "skuAttribute": {"type": "string", "description": "规格"}, "status": {"type": "integer", "format": "int32", "description": "商品状态 1-正常，2-下架，3-删除"}}, "description": "S<PERSON>"}, "RespResult«List«SkuAttribute»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/SkuAttribute"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«SkuAttribute»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/SkuAttribute"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "SkuAttribute": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32", "description": "ID"}, "name": {"type": "string", "description": "属性名称"}, "options": {"type": "string", "description": "属性选项"}, "sort": {"type": "integer", "format": "int32", "description": "排序"}, "categoryId": {"type": "string", "description": "分类ID集合"}}, "description": "SkuAttribute"}, "RespResult«List«Spu»»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,20001:失败,20002:用户名或密码错误,20003:权限不足,20004:远程调用失败,20005:重复操作,20006:没有对应的数据"}, "data": {"type": "array", "description": "逻辑数据", "items": {"$ref": "#/definitions/Spu"}}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "RespResult«Spu»": {"type": "object", "required": ["code", "data", "message"], "properties": {"code": {"type": "integer", "format": "int32", "description": "返回状态码,20000:成功,50000:失败"}, "data": {"description": "逻辑数据", "$ref": "#/definitions/Spu"}, "message": {"type": "string", "description": "提示信息"}}, "description": "RespResult"}, "Spu": {"type": "object", "properties": {"id": {"type": "string", "description": "主键"}, "name": {"type": "string", "description": "SPU名"}, "intro": {"type": "string", "description": "简介"}, "brandId": {"type": "integer", "format": "int32", "description": "品牌ID"}, "categoryOneId": {"type": "integer", "format": "int32", "description": "一级分类"}, "categoryTwoId": {"type": "integer", "format": "int32", "description": "二级分类"}, "categoryThreeId": {"type": "integer", "format": "int32", "description": "三级分类"}, "images": {"type": "string", "description": "图片列表"}, "afterSalesService": {"type": "string", "description": "售后服务"}, "content": {"type": "string", "description": "介绍"}, "attributeList": {"type": "string", "description": "规格列表"}, "isMarketable": {"type": "integer", "format": "int32", "description": "是否上架,0已下架，1已上架"}, "isDelete": {"type": "integer", "format": "int32", "description": "是否删除,0:未删除，1：已删除"}, "status": {"type": "integer", "format": "int32", "description": "审核状态，0：未审核，1：已审核，2：审核不通过"}}, "description": "Spu"}}, "externalDocs": {"description": "云商城", "url": "admin"}}